import React, { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectAuthToken } from '../../../../store/features/auth/authSlice';

// Import components
import ContractTypeSelection from './ContractTypes/ContractTypeSelection';
import FormWizard from './FormWizard/FormWizard';
import PreviewPane from './PreviewPane/PreviewPane';
import LoadingSteps from './common/LoadingSteps';
import ContractResult from './common/ContractResult';

// Import utilities
import { saveToLocalStorage, loadFromLocalStorage, clearLocalStorage } from './utils/localStorage';
import { CONTRACT_TYPES } from './utils/contractConstants';

const LegalContractGenerator = () => {
  const dispatch = useDispatch();
  const token = useSelector(selectAuthToken);

  // Main application state
  const [currentStep, setCurrentStep] = useState('selection'); // 'selection', 'form', 'preview', 'loading', 'result'
  const [selectedContractType, setSelectedContractType] = useState(null);
  const [formData, setFormData] = useState({});
  const [generatedContract, setGeneratedContract] = useState('');
  const [error, setError] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // Load saved data on component mount
  useEffect(() => {
    const savedData = loadFromLocalStorage('legalContractDraft');
    if (savedData) {
      setSelectedContractType(savedData.contractType);
      setFormData(savedData.formData || {});
      if (savedData.contractType) {
        setCurrentStep('form');
      }
    }
  }, []);

  // Save data to localStorage whenever it changes
  useEffect(() => {
    if (selectedContractType || Object.keys(formData).length > 0) {
      saveToLocalStorage('legalContractDraft', {
        contractType: selectedContractType,
        formData: formData,
        timestamp: Date.now()
      });
    }
  }, [selectedContractType, formData]);

  // Handle contract type selection
  const handleContractTypeSelect = useCallback((contractType) => {
    setSelectedContractType(contractType);
    setFormData({
      contractType: contractType,
      language: 'English',
      jurisdiction: 'United States'
    });
    setCurrentStep('form');
    setError(null);
  }, []);

  // Handle form data updates
  const handleFormDataUpdate = useCallback((newData) => {
    setFormData(prev => ({ ...prev, ...newData }));
  }, []);

  // Handle form submission and contract generation
  const handleGenerateContract = async (finalFormData) => {
    setError(null);
    setIsGenerating(true);
    setCurrentStep('loading');

    try {
      const response = await fetch('/api/legal-contracts/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(finalFormData),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      setGeneratedContract(data.contract);
      setCurrentStep('result');
      
      // Clear the draft after successful generation
      clearLocalStorage('legalContractDraft');
      
    } catch (err) {
      setError(err.message);
      setCurrentStep('form');
      console.error('Contract Generation Error:', err);
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle starting over
  const handleStartOver = useCallback(() => {
    setCurrentStep('selection');
    setSelectedContractType(null);
    setFormData({});
    setGeneratedContract('');
    setError(null);
    clearLocalStorage('legalContractDraft');
  }, []);

  // Handle going back to form
  const handleBackToForm = useCallback(() => {
    setCurrentStep('form');
    setError(null);
  }, []);

  // Render based on current step
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'selection':
        return (
          <ContractTypeSelection 
            onSelectType={handleContractTypeSelect}
            contractTypes={CONTRACT_TYPES}
          />
        );
      
      case 'form':
        return (
          <FormWizard
            contractType={selectedContractType}
            formData={formData}
            onFormDataUpdate={handleFormDataUpdate}
            onGenerate={handleGenerateContract}
            onBack={handleStartOver}
            error={error}
          />
        );
      
      case 'loading':
        return <LoadingSteps contractType={selectedContractType} />;
      
      case 'result':
        return (
          <ContractResult
            contract={generatedContract}
            contractType={selectedContractType}
            formData={formData}
            onStartOver={handleStartOver}
            onBackToForm={handleBackToForm}
          />
        );
      
      default:
        return (
          <ContractTypeSelection 
            onSelectType={handleContractTypeSelect}
            contractTypes={CONTRACT_TYPES}
          />
        );
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto">
      {renderCurrentStep()}
    </div>
  );
};

export default LegalContractGenerator;
