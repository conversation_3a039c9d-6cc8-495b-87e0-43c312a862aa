import React, { Suspense, lazy } from 'react';
import { FiFileText, FiLoader } from 'react-icons/fi';

// Lazily import the main component
const LegalContractGenerator = lazy(() => import('./LegalContractGenerator/LegalContractGenerator'));

// Loading component for the form
const ContractLoader = () => (
  <div className="w-full bg-slate-900/50 border border-slate-700 rounded-2xl p-8 flex flex-col items-center text-center animate-fade-in">
    <FiLoader className="w-8 h-8 text-blue-400 animate-spin mb-4" />
    <h3 className="text-xl font-bold text-blue-400 mb-2">Loading Legal Contract Generator</h3>
    <p className="text-slate-400">Preparing your professional contract creation interface...</p>
  </div>
);

const LegalContractGeneratorPage = () => {
  return (
    <div className="container justify-center items-center mx-auto text-white w-full min-h-full flex flex-col px-4">
      <main className="w-full flex-grow flex flex-col max-w-7xl justify-center py-10 md:py-20">
        <div className="bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700 p-6 md:p-10 flex flex-col items-center">
          <div className="flex items-center justify-center w-16 h-16 rounded-full bg-blue-500/10 border border-blue-400/30 mb-6">
            <FiFileText className="w-8 h-8 text-blue-400" />
          </div>
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-3 text-center">
            Legal Contract Generator
          </h1>
          <p className="text-slate-400 text-center max-w-2xl mb-10">
            Create professional legal contracts with AI-powered assistance. Generate comprehensive agreements 
            tailored to your business needs with intelligent clause recommendations and legal compliance guidance.
          </p>

          <Suspense fallback={<ContractLoader />}>
            <LegalContractGenerator />
          </Suspense>
        </div>
      </main>
    </div>
  );
};

export default LegalContractGeneratorPage;
