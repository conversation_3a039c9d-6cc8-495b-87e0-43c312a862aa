import React from 'react';
import BasicInfoStep from './steps/BasicInfoStep';
import ServiceScopeStep from './steps/ServiceScopeStep';
import ServiceTermsStep from './steps/ServiceTermsStep';
import ServiceLegalStep from './steps/ServiceLegalStep';
import PartnershipBasicStep from './steps/PartnershipBasicStep';
import PartnershipFinancialStep from './steps/PartnershipFinancialStep';
import PartnershipManagementStep from './steps/PartnershipManagementStep';
import PartnershipLegalStep from './steps/PartnershipLegalStep';
import NDABasicStep from './steps/NDABasicStep';
import {
  NDAConfidentialStep,
  NDAObligationsStep,
  NDATermsStep,
  FreelanceBasicStep,
  FreelanceWorkStep,
  FreelancePaymentStep,
  FreelanceLegalStep,
  SupplierBasicStep,
  SupplierProductsStep,
  SupplierTermsStep,
  SupplierQualityStep,
  EmploymentBasicStep,
  EmploymentCompensationStep,
  EmploymentDutiesStep,
  EmploymentPoliciesStep,
  LeaseBasicStep,
  LeaseFinancialStep,
  LeaseConditionsStep,
  LeaseLegalStep
} from './steps/placeholderSteps';

const FormStep = ({ contractType, stepId, formData, onFormDataChange, validationErrors }) => {
  const stepKey = `${contractType}_${stepId}`;

  // Map contract types and steps to their respective components
  const stepComponents = {
    // Service Agreement Steps
    service_basic: BasicInfoStep,
    service_scope: ServiceScopeStep,
    service_terms: ServiceTermsStep,
    service_legal: ServiceLegalStep,

    // Partnership Agreement Steps
    partnership_basic: PartnershipBasicStep,
    partnership_financial: PartnershipFinancialStep,
    partnership_management: PartnershipManagementStep,
    partnership_legal: PartnershipLegalStep,

    // NDA Steps
    nda_basic: NDABasicStep,
    nda_confidential: NDAConfidentialStep,
    nda_obligations: NDAObligationsStep,
    nda_terms: NDATermsStep,

    // Freelance Agreement Steps
    freelance_basic: FreelanceBasicStep,
    freelance_work: FreelanceWorkStep,
    freelance_payment: FreelancePaymentStep,
    freelance_legal: FreelanceLegalStep,

    // Supplier Agreement Steps
    supplier_basic: SupplierBasicStep,
    supplier_products: SupplierProductsStep,
    supplier_terms: SupplierTermsStep,
    supplier_quality: SupplierQualityStep,

    // Employment Contract Steps
    employment_basic: EmploymentBasicStep,
    employment_compensation: EmploymentCompensationStep,
    employment_duties: EmploymentDutiesStep,
    employment_policies: EmploymentPoliciesStep,

    // Lease Agreement Steps
    lease_basic: LeaseBasicStep,
    lease_financial: LeaseFinancialStep,
    lease_conditions: LeaseConditionsStep,
    lease_legal: LeaseLegalStep
  };

  const StepComponent = stepComponents[stepKey];

  if (!StepComponent) {
    return (
      <div className="text-center py-8">
        <p className="text-slate-400">Step component not found for {stepKey}</p>
      </div>
    );
  }

  return (
    <div className="animate-fade-in">
      <StepComponent
        formData={formData}
        onFormDataChange={onFormDataChange}
        validationErrors={validationErrors}
        contractType={contractType}
      />
    </div>
  );
};

export default FormStep;
